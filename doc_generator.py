#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
功能需求文档生成器

基于COSMIC功能拆解结果生成功能需求文档，包括：
1. 功能需求描述
2. 关键时序图（Mermaid语法）
3. 分批次处理，每批次最多50个子过程
4. 支持配置起始序号
"""

import pandas as pd
import json
import os
import time
import re
from typing import List, Dict, Any, Tuple
from collections import defaultdict
from main import call_LLM
from knowledge_base import knowledge_base
from config import KNOWLEDGE_BASE_ENABLED, REQUIREMENT_GENERATOR_CONFIG


class RequirementGenerator:
    """功能需求文档生成器"""
    
    def __init__(self, start_number: str = None, max_subprocess_per_batch: int = None):
        """
        初始化生成器

        Args:
            start_number: 起始序号，如"2"表示从2.1开始，"3.1"表示从3.1.1开始
            max_subprocess_per_batch: 每批次最大子过程数量
        """
        # 使用配置文件中的默认值
        config = REQUIREMENT_GENERATOR_CONFIG
        self.start_number = start_number or config["default_start_number"]
        self.max_subprocess_per_batch = max_subprocess_per_batch or config["max_subprocess_per_batch"]
        self.prompt_file = config["prompt_file"]
        self.output_dir = config["output_dir"]
        self.column_mapping = config["column_mapping"]
        
        # 加载系统提示词
        self.load_prompt()
        
        # 序号计数器
        self.level1_counter = 1
        self.level2_counter = 1
        self.level3_counter = 1
        self.function_counter = 1
        
        # 解析起始序号
        self.parse_start_number()
    
    def load_prompt(self):
        """加载系统提示词"""
        try:
            with open(self.prompt_file, 'r', encoding='utf-8') as f:
                self.system_prompt = f.read()
            print(f"成功加载系统提示词: {self.prompt_file}")
        except FileNotFoundError:
            print(f"警告: 系统提示词文件 {self.prompt_file} 不存在")
            self.system_prompt = "你是一位资深的软件需求分析师，请根据COSMIC功能拆解数据生成功能需求文档。"
    
    def parse_start_number(self):
        """解析起始序号"""
        parts = self.start_number.split('.')
        if len(parts) == 1:
            # 如"2"，表示从2.1开始
            self.base_number = parts[0]
            self.level1_counter = 1
        elif len(parts) == 2:
            # 如"3.1"，表示从3.1.1开始
            self.base_number = parts[0]
            self.level1_counter = int(parts[1])
        else:
            # 默认处理
            self.base_number = "2"
            self.level1_counter = 1
    
    def load_cosmic_data(self, file_path: str) -> pd.DataFrame:
        """
        加载COSMIC功能拆解数据
        
        Args:
            file_path: 文件路径
            
        Returns:
            DataFrame: 加载的数据
        """
        try:
            # 检测文件类型
            file_extension = os.path.splitext(file_path)[1].lower()
            if file_extension == '.csv':
                df = pd.read_csv(file_path, encoding='utf-8-sig')
            elif file_extension == '.xlsx':
                # 读取XLS文件内容
                sheet_name = 0
                header = 0
                df = pd.read_excel(file_path, sheet_name = sheet_name, header = header)
                # 向后填充有合并的数据列
                level_1, level_2, level_3, func_user,event_name,func_name = "一级功能模块", "二级功能模块","三级功能模块","功能用户","触发事件","功能过程"
                df[[level_1, level_2, level_3, func_user,event_name,func_name]] = df[[level_1, level_2, level_3, func_user,event_name,func_name]].ffill()            
            
            print(f"成功加载COSMIC数据行数: {len(df)}")
            return df
        except Exception as e:
            print(f"加载COSMIC数据失败: {e}")
            return pd.DataFrame()
    
    def organize_data_by_hierarchy(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        按层级结构组织数据

        Args:
            df: 原始数据DataFrame

        Returns:
            Dict: 按层级组织的数据结构
        """
        # 使用配置文件中的列名映射
        level1_col = self.column_mapping["level1"]
        level2_col = self.column_mapping["level2"]
        level3_col = self.column_mapping["level3"]
        function_col = self.column_mapping["function"]
        
        # 检查必要列是否存在
        required_cols = [level1_col, level2_col, level3_col, function_col]
        missing_cols = [col for col in required_cols if col not in df.columns]
        if missing_cols:
            print(f"警告: 缺少必要列: {missing_cols}")
            print(f"可用列: {list(df.columns)}")
            return {}
        
        # 组织数据结构
        hierarchy = defaultdict(lambda: defaultdict(lambda: defaultdict(list)))
        
        for _, row in df.iterrows():
            level1 = str(row[level1_col]).strip()
            level2 = str(row[level2_col]).strip()
            level3 = str(row[level3_col]).strip()
            
            # 跳过空值
            if pd.isna(level1) or pd.isna(level2) or pd.isna(level3):
                continue
            if level1.lower() in ['nan', 'none', ''] or level2.lower() in ['nan', 'none', ''] or level3.lower() in ['nan', 'none', '']:
                continue
            
            # 将整行数据添加到对应的三级模块下
            hierarchy[level1][level2][level3].append(row.to_dict())
        
        return dict(hierarchy)
    
    def create_batches(self, hierarchy: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        创建批次，每批次最多包含指定数量的子过程
        
        Args:
            hierarchy: 按层级组织的数据
            
        Returns:
            List[Dict]: 批次列表
        """
        batches = []
        current_batch = defaultdict(lambda: defaultdict(lambda: defaultdict(list)))
        current_subprocess_count = 0
        
        for level1, level2_data in hierarchy.items():
            for level2, level3_data in level2_data.items():
                for level3, functions in level3_data.items():
                    # 计算当前三级模块的子过程数量
                    subprocess_count = len(functions)
                    
                    # 如果加入当前三级模块会超过限制，先保存当前批次
                    if current_subprocess_count + subprocess_count > self.max_subprocess_per_batch and current_subprocess_count > 0:
                        batches.append(dict(current_batch))
                        current_batch = defaultdict(lambda: defaultdict(lambda: defaultdict(list)))
                        current_subprocess_count = 0
                    
                    # 将当前三级模块添加到批次中
                    current_batch[level1][level2][level3] = functions
                    current_subprocess_count += subprocess_count
        
        # 添加最后一个批次
        if current_subprocess_count > 0:
            batches.append(dict(current_batch))
        
        print(f"创建了 {len(batches)} 个批次")
        for i, batch in enumerate(batches, 1):
            subprocess_count = sum(
                len(functions)
                for level2_data in batch.values()
                for level3_data in level2_data.values()
                for functions in level3_data.values()
            )
            print(f"  批次 {i}: {subprocess_count} 个子过程")
        
        return batches
    
    def format_batch_for_llm(self, batch: Dict[str, Any]) -> str:
        """
        将批次数据格式化为LLM输入，按照新的目录结构要求

        Args:
            batch: 批次数据

        Returns:
            str: 格式化的输入文本
        """
        lines = []
        lines.append("请基于以下COSMIC功能拆解数据生成功能需求文档：")
        lines.append("")
        lines.append(f"请按照新的目录结构格式生成，起始序号为 {self.base_number}：")
        lines.append("")
        lines.append("重要约束：")
        lines.append("1. 只为三级模块生成时序图，不为功能过程生成时序图")
        lines.append("2. 从三级模块逐层向上组织文档内容")
        lines.append("3. 时序图应综合该三级模块下所有功能过程的业务流程")
        lines.append("")

        # 按层级组织数据，并为每个功能过程收集所有子过程
        for level1, level2_data in batch.items():
            lines.append(f"# 一级模块: {level1}")
            lines.append("")

            for level2, level3_data in level2_data.items():
                lines.append(f"## 二级模块: {level2}")
                lines.append("")

                for level3, functions in level3_data.items():
                    lines.append(f"### 三级模块: {level3}")
                    lines.append("")

                    # 按功能过程分组
                    function_groups = defaultdict(list)
                    for func_data in functions:
                        func_process = func_data.get('功能过程', 'N/A')
                        function_groups[func_process].append(func_data)

                    # 列出该三级模块包含的所有功能过程
                    lines.append("该三级模块包含的功能过程：")
                    for func_process in function_groups.keys():
                        lines.append(f"- {func_process}")
                    lines.append("")

                    # 详细描述每个功能过程
                    for func_process, func_list in function_groups.items():
                        lines.append(f"#### 功能过程: {func_process}")

                        # 获取功能过程级别的信息（从第一条记录）
                        first_record = func_list[0]
                        lines.append(f"- 功能用户: {first_record.get('功能用户', 'N/A')}")
                        lines.append(f"- 触发事件: {first_record.get('触发事件', 'N/A')}")
                        lines.append("")

                        lines.append("子过程列表:")
                        for i, func_data in enumerate(func_list, 1):
                            lines.append(f"{i}. 子过程描述: {func_data.get('子过程描述', 'N/A')}")
                            lines.append(f"   - 数据移动类型: {func_data.get('数据移动类型', 'N/A')}")
                            lines.append(f"   - 数据组: {func_data.get('数据组', 'N/A')}")
                            lines.append(f"   - 数据属性: {func_data.get('数据属性', 'N/A')}")
                        lines.append("")

        return "\n".join(lines)
    
    def get_knowledge_context(self, batch: Dict[str, Any]) -> str:
        """
        获取批次相关的知识库上下文
        
        Args:
            batch: 批次数据
            
        Returns:
            str: 知识库上下文
        """
        if not KNOWLEDGE_BASE_ENABLED or not knowledge_base.enabled:
            return ""
        
        # 收集所有相关的查询词
        query_parts = []
        for level1, level2_data in batch.items():
            for level2, level3_data in level2_data.items():
                for level3, functions in level3_data.items():
                    query_parts.extend([level1, level2, level3])
                    for func_data in functions:
                        if func_data.get('功能过程'):
                            query_parts.append(func_data['功能过程'])
        
        # 构建查询
        query = " ".join(set(query_parts))
        context = knowledge_base.get_context_for_module(query)
        
        if context:
            print(f"获取到知识库上下文: {len(context)} 字符")
        
        return context
    def _remove_think(self, text):
        """
        移除字符串中所有<ai>和</ai>标签及其之间的内容。

        参数:
            text (str): 输入的字符串

        返回:
            str: 处理后的字符串
        """
        pattern = r'<think>.*?</think>'
        while True:
            new_text = re.sub(pattern, '', text, flags=re.DOTALL)
            if new_text == text:
                break
            text = new_text
        if all(c in ('\n', ' ') for c in text):
            text = text.replace('\n', '').replace(' ', '')
        return text
    
    def generate_batch_requirements(self, batch: Dict[str, Any], batch_index: int) -> str:
        """
        为单个批次生成功能需求文档

        Args:
            batch: 批次数据
            batch_index: 批次索引

        Returns:
            str: 生成的Markdown文档
        """
        print(f"开始处理批次 {batch_index}...")

        # 格式化输入，包含序号信息
        user_input = self.format_batch_for_llm(batch)

        # 添加序号指导信息
        numbering_guide = f"""
请严格按照以下序号格式生成文档：

当前序号状态：
- 一级模块序号: {self.base_number}.{self.level1_counter}
- 二级模块序号: {self.base_number}.{self.level1_counter}.{self.level2_counter}
- 三级模块序号: {self.base_number}.{self.level1_counter}.{self.level2_counter}.{self.level3_counter}
- 功能过程序号: {self.base_number}.{self.level1_counter}.{self.level2_counter}.{self.level3_counter}.{self.function_counter}

请按照数据中的层级结构，自动递增序号。每个新的一级模块时，二级、三级、功能序号重置为1。
每个新的二级模块时，三级、功能序号重置为1。每个新的三级模块时，功能序号重置为1。

{user_input}
"""

        # 获取知识库上下文
        knowledge_context = "" #self.get_knowledge_context(batch)

        # 调用大模型生成
        try:
            result = call_LLM(self.system_prompt, numbering_guide, knowledge_context)
            print(f"批次 {batch_index} 生成完成")
            # 去除<think> 和 </think>之间的内容（包括 <think> 和 </think>）
            result = self._remove_think(result)
            
            # 用正则表达式提取 ```markdown ... ``` 之间的内容
            match = re.search(r"```markdown\s*(.*?)\s*```", result, re.DOTALL)
            if match:
                content = match.group(1)
            else:
                content = result

            # 更新序号计数器（简单递增，实际应该根据生成内容分析）
            self.update_counters_after_batch(batch)

            return content
        except Exception as e:
            print(f"批次 {batch_index} 生成失败: {e}")
            return f"# 批次 {batch_index} 生成失败\n\n错误信息: {str(e)}\n\n"

    def update_counters_after_batch(self, batch: Dict[str, Any]):
        """
        根据批次内容更新序号计数器

        Args:
            batch: 批次数据
        """
        # 统计批次中的各级模块数量
        level1_count = len(batch)
        level2_count = sum(len(level2_data) for level2_data in batch.values())
        level3_count = sum(
            len(level3_data)
            for level2_data in batch.values()
            for level3_data in level2_data.values()
        )
        function_count = sum(
            len(functions)
            for level2_data in batch.values()
            for level3_data in level2_data.values()
            for functions in level3_data.values()
        )

        # 更新计数器（这里简化处理，实际应该更精确）
        self.level1_counter += level1_count
        # 注意：这里的逻辑需要根据实际的层级结构来调整
    
    def generate_requirements_document(self, csv_file: str, output_file: str = None) -> str:
        """
        生成完整的功能需求文档，按照新的目录结构

        Args:
            csv_file: COSMIC数据CSV文件路径
            output_file: 输出文件路径，如果为None则自动生成

        Returns:
            str: 输出文件路径
        """
        print("开始生成功能需求文档...")

        # 加载数据
        df = self.load_cosmic_data(csv_file)
        if df.empty:
            print("数据加载失败，无法生成文档")
            return ""

        # 组织数据
        hierarchy = self.organize_data_by_hierarchy(df)
        if not hierarchy:
            print("数据组织失败，无法生成文档")
            return ""

        # 创建批次
        batches = self.create_batches(hierarchy)
        if not batches:
            print("批次创建失败，无法生成文档")
            return ""

        # 生成输出文件名
        if output_file is None:
            base_name = os.path.splitext(os.path.basename(csv_file))[0]
            output_file = f"{base_name}_功能需求文档.md"

        # 生成文档
        all_content = []
        all_content.append(f"# 功能需求文档")
        all_content.append("")
        all_content.append(f"基于COSMIC功能拆解数据生成的功能需求文档")
        all_content.append(f"生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        all_content.append("")
        all_content.append("本文档按照新的目录结构组织，从三级模块逐层向上组织内容，只为三级模块生成时序图。")
        all_content.append("")

        # 生成批次内容并合并
        batch_contents = []
        for i, batch in enumerate(batches, 1):
            print(f"正在处理批次 {i}/{len(batches)}...")
            batch_content = self.generate_batch_requirements(batch, i)
            batch_contents.append(batch_content)

            # 添加延时避免API限制
            if i < len(batches):
                time.sleep(1)

        # 合并所有批次内容
        merged_content = self.merge_batch_contents(batch_contents, hierarchy)
        all_content.append(merged_content)

        # 写入文件
        final_content = "\n".join(all_content)
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(final_content)

        print(f"功能需求文档生成完成: {output_file}")
        return output_file

    def merge_batch_contents(self, batch_contents: List[str], hierarchy: Dict[str, Any]) -> str:
        """
        合并批次内容，按照新的目录结构组织

        Args:
            batch_contents: 各批次生成的内容列表
            hierarchy: 原始层级数据结构

        Returns:
            str: 合并后的内容
        """
        # 简单合并所有批次内容
        # 在实际应用中，这里可以进行更复杂的内容整理和去重
        merged_lines = []

        for i, content in enumerate(batch_contents, 1):
            if content.strip():
                #merged_lines.append(f"<!-- 批次 {i} 内容开始 -->")
                merged_lines.append(content)
                #merged_lines.append(f"<!-- 批次 {i} 内容结束 -->")
                merged_lines.append("")

        return "\n".join(merged_lines)


def main():
    """主函数"""
    import sys

    # 检查命令行参数
    if len(sys.argv) > 1:
        csv_file = sys.argv[1] # COSMIC数据文件
    else:
        csv_file = "output.csv"

    config = REQUIREMENT_GENERATOR_CONFIG
    # 配置参数
    start_number = config["default_start_number"]       # 起始序号，可以是"2"或"3.1"等格式
    max_subprocess_per_batch = config["max_subprocess_per_batch"]  # 每批次最大子过程数

    print("=== 功能需求文档生成器 ===")
    print(f"输入文件: {csv_file}")
    print(f"起始序号: {start_number}")
    print(f"每批次最大子过程数: {max_subprocess_per_batch}")
    print()

    # 检查输入文件是否存在
    if not os.path.exists(csv_file):
        print(f"❌ 输入文件不存在: {csv_file}")
        print("请确保COSMIC数据文件存在")
        return

    # 创建生成器
    generator = RequirementGenerator(
        start_number=start_number,
        max_subprocess_per_batch=max_subprocess_per_batch
    )

    # 生成文档
    output_file = generator.generate_requirements_document(csv_file)

    if output_file:
        print(f"\n✅ 功能需求文档生成成功!")
        print(f"📄 输出文件: {output_file}")
        print(f"📊 文件大小: {os.path.getsize(output_file)} 字节")
    else:
        print("\n❌ 功能需求文档生成失败!")


if __name__ == "__main__":
    main()
