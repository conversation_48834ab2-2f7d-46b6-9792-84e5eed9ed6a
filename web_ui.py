#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
COSMIC功能需求文档生成器 Web UI

提供Web界面用于：
1. 选择xlsx/csv文件
2. 生成markdown文档
3. 在线渲染markdown（支持Mermaid时序图）
4. 重新生成已存在的文档
"""

import os
import glob
import json
import time
from flask import Flask, render_template, request, jsonify, send_file
from flask_cors import CORS
import markdown
from pygments import highlight
from pygments.lexers import get_lexer_by_name
from pygments.formatters import html
from doc_generator import RequirementGenerator
from config import REQUIREMENT_GENERATOR_CONFIG

app = Flask(__name__)
CORS(app)

# 配置
DEBUG_DIR = "debug"
SUPPORTED_EXTENSIONS = ['.xlsx', '.csv']

def get_available_files():
    """获取项目目录下所有支持的文件"""
    files = []
    for ext in SUPPORTED_EXTENSIONS:
        pattern = f"*{ext}"
        found_files = glob.glob(pattern)
        for file_path in found_files:
            file_info = {
                'name': os.path.basename(file_path),
                'path': file_path,
                'extension': ext,
                'size': os.path.getsize(file_path),
                'modified': os.path.getmtime(file_path)
            }
            
            # 检查对应的markdown文件是否存在
            base_name = os.path.splitext(file_path)[0]
            md_file = f"{base_name}_功能需求文档.md"
            file_info['has_markdown'] = os.path.exists(md_file)
            file_info['markdown_path'] = md_file
            
            files.append(file_info)
    
    # 按修改时间排序
    files.sort(key=lambda x: x['modified'], reverse=True)
    return files

def render_markdown_with_mermaid(markdown_content):
    """渲染Markdown内容，支持Mermaid图表"""
    # 使用markdown库渲染基本内容
    md = markdown.Markdown(extensions=['codehilite', 'fenced_code', 'tables', 'toc'])
    html_content = md.convert(markdown_content)
    
    # 包装在完整的HTML页面中，包含Mermaid支持
    html_template = f"""
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>功能需求文档</title>
        <script src="https://cdn.jsdelivr.net/npm/mermaid@10.9.1/dist/mermaid.min.js"></script>
        <style>
            body {{
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
                line-height: 1.6;
                color: #333;
                max-width: 1200px;
                margin: 0 auto;
                padding: 20px;
                background-color: #f8f9fa;
            }}
            .container {{
                background: white;
                padding: 30px;
                border-radius: 8px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }}
            h1, h2, h3, h4, h5, h6 {{
                color: #2c3e50;
                margin-top: 2em;
                margin-bottom: 1em;
            }}
            h1 {{ border-bottom: 3px solid #3498db; padding-bottom: 10px; }}
            h2 {{ border-bottom: 2px solid #e74c3c; padding-bottom: 8px; }}
            h3 {{ border-bottom: 1px solid #f39c12; padding-bottom: 5px; }}
            code {{
                background-color: #f4f4f4;
                padding: 2px 4px;
                border-radius: 3px;
                font-family: 'Courier New', monospace;
            }}
            pre {{
                background-color: #f8f8f8;
                border: 1px solid #ddd;
                border-radius: 5px;
                padding: 15px;
                overflow-x: auto;
            }}
            .mermaid {{
                text-align: center;
                margin: 20px 0;
            }}
            table {{
                border-collapse: collapse;
                width: 100%;
                margin: 20px 0;
            }}
            th, td {{
                border: 1px solid #ddd;
                padding: 12px;
                text-align: left;
            }}
            th {{
                background-color: #f2f2f2;
                font-weight: bold;
            }}
            .back-button {{
                position: fixed;
                top: 20px;
                right: 20px;
                background: #3498db;
                color: white;
                padding: 10px 20px;
                border: none;
                border-radius: 5px;
                cursor: pointer;
                text-decoration: none;
                font-size: 14px;
            }}
            .back-button:hover {{
                background: #2980b9;
            }}
        </style>
    </head>
    <body>
        <a href="/" class="back-button">返回首页</a>
        <div class="container">
            {html_content}
        </div>
        <script>
            mermaid.initialize({{
                startOnLoad: true,
                theme: 'default',
                securityLevel: 'loose',
                sequence: {{
                    diagramMarginX: 50,
                    diagramMarginY: 10,
                    actorMargin: 50,
                    width: 150,
                    height: 65,
                    boxMargin: 10,
                    boxTextMargin: 5,
                    noteMargin: 10,
                    messageMargin: 35,
                    mirrorActors: true,
                    bottomMarginAdj: 1,
                    useMaxWidth: true,
                    rightAngles: false,
                    showSequenceNumbers: false
                }},
                flowchart: {{
                    useMaxWidth: true,
                    htmlLabels: true
                }}
            }});

            // 确保在DOM加载完成后重新渲染
            document.addEventListener('DOMContentLoaded', function() {{
                mermaid.init();
            }});
        </script>
    </body>
    </html>
    """
    
    return html_template

@app.route('/')
def index():
    """主页面"""
    files = get_available_files()
    return render_template('index.html', files=files)

@app.route('/api/files')
def api_files():
    """获取文件列表API"""
    files = get_available_files()
    return jsonify(files)

@app.route('/api/generate', methods=['POST'])
def api_generate():
    """生成markdown文档API"""
    try:
        data = request.get_json()
        file_path = data.get('file_path')
        force_regenerate = data.get('force_regenerate', False)
        
        if not file_path or not os.path.exists(file_path):
            return jsonify({'error': '文件不存在'}), 400
        
        # 检查markdown文件是否已存在
        base_name = os.path.splitext(file_path)[0]
        md_file = f"{base_name}_功能需求文档.md"
        
        if os.path.exists(md_file) and not force_regenerate:
            return jsonify({
                'success': True,
                'message': 'Markdown文件已存在',
                'markdown_path': md_file,
                'already_exists': True
            })
        
        # 创建生成器并生成文档
        generator = RequirementGenerator()
        output_file = generator.generate_requirements_document(file_path, md_file)
        
        if output_file:
            return jsonify({
                'success': True,
                'message': '文档生成成功',
                'markdown_path': output_file,
                'already_exists': False
            })
        else:
            return jsonify({'error': '文档生成失败'}), 500
            
    except Exception as e:
        return jsonify({'error': f'生成失败: {str(e)}'}), 500

@app.route('/api/render/<path:filename>')
def api_render(filename):
    """渲染markdown文件API"""
    try:
        if not os.path.exists(filename):
            return jsonify({'error': '文件不存在'}), 404
        
        with open(filename, 'r', encoding='utf-8') as f:
            markdown_content = f.read()
        
        html_content = render_markdown_with_mermaid(markdown_content)
        return html_content
        
    except Exception as e:
        return jsonify({'error': f'渲染失败: {str(e)}'}), 500

@app.route('/download/<path:filename>')
def download_file(filename):
    """下载文件"""
    try:
        if os.path.exists(filename):
            return send_file(filename, as_attachment=True)
        else:
            return jsonify({'error': '文件不存在'}), 404
    except Exception as e:
        return jsonify({'error': f'下载失败: {str(e)}'}), 500

if __name__ == '__main__':
    # 确保debug目录存在
    os.makedirs(DEBUG_DIR, exist_ok=True)
    
    # 启动应用
    print("=== COSMIC功能需求文档生成器 Web UI ===")
    print("访问地址: http://localhost:5000")
    print("支持的文件格式: .xlsx, .csv")
    print("功能:")
    print("  1. 选择数据文件")
    print("  2. 生成功能需求文档")
    print("  3. 在线渲染Markdown（支持Mermaid时序图）")
    print("  4. 重新生成已存在的文档")
    print()
    
    app.run(debug=True, host='0.0.0.0', port=5000)
