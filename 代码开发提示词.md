# 用户手册知识库切块逻辑
重新修改用户手册知识库构建的切块逻辑：
1.按照markdown的语法，构建层次结构
2.每个子块的层级为：config.py中的MARKDOWN_CHILD_PATTERN参数,默认4个# 四级标题。
3.如果上级标题（3个#）内没有四级标题，则该三级标题作为单独的子块。
4.以此类推，再上一级标题（2个#）内没有3级标题，则该二级标题作为单独的子块。
5.父标题（如3个#）与它的第1个子标题（如4个#）之间存在内容，则该父标题作为单独的子块。
示例如下：
```markdown
# 1.章节
这是测试子块1
## 1.1 子章节
这是测试子块2
## 1.2 子章节
这是测试子块3
# 2.章节
## 2.1 子章节
这是测试子块4
# 3.章节
这是测试子块5
##  3.1 子章节
这是测试子块6
### 3.1.1 子章节
#### 3.1.1.1 子章节
这是测试子块7
#### 3.1.1.2 子章节
这是测试子块8
```
将拆分为如下子块：
块1： 1.章节 测试子块1
块2： 1.1 子章节 测试子块2
块3： 1.2 子章节 测试子块3
块5： 2.1 子章节 测试子块4
块6： 3.章节 测试子块5
块7： 3.1 子章节 测试子块6
块8： 3.1.1.1 子章节 测试子块7
块9： 3.1.1.2 子章节 测试子块8


# cosmic校验功能
1. 增加新的python文件，实现cosmic校验功能，使用大模型检查用户提交的cosmic功能拆解是否符合规范。
2. 用户提交的文件是csv格式，参考output-new.csv，解析后可组织为json的层次结构（可以配置去除不必要的字段），减少提示词使用。
3. 大模型的配置依然使用config.py,增加用于检查cosmic功能的大模型配置项。
4. 整个cosmic的数据一次性提交给大模型检查, 输出大模型的检查结果，对不符合的功能过程或子过程可给出修改建议。
5. 根据要求实现功能，系统提示词使用独立的文件：check_prompt.md

# 新增python文件，实现功能需求文档生成
1. 根据cosmic功能拆解的文档，由大模型辅助生成文字性的功能需求描述和关键时序图。
2. 使用单独的系统提示词文件，用于生成功能需求描述和关键时序图。
3. 生成的功能需求文档为markdown格式，结构如下：
```json
## 2.1 一级功能需求 （对应一级模块）
### 2.1.1 二级功能需求 （对应二级模块）
#### 2.1.1.1 三级功能需求 （对应三级模块）
##### 2.1.1.1.1 功能过程 （对应功能过程）
###### 2.1.1.1.1.1 关键时序图/业务逻辑图
（结合触发事件、子过程、知识库的相关信息，生成可渲染的Mermaid语法图）
###### 2.1.1.1.1.2 需求描述 
（结合子过程、数据组及属性及知识库的相关信息，描述该功能过程）
## 2.2 一级功能需求 （对应一级模块）
....（省略）

```
4. 大模型请求可直接调用main.py中的call_LLM方法，知识库使用knowledge_base.py的get_context_for_module方法
5. 分批次生成功能文档，每个批次可包含多个三级模块(最多50个子过程)。
6. markdown文档的起始序号可配置，比如: 起始=3.1，后面的一级模块依次为：3.2、3.3。

## 优化
1. 增加一个WEB UI的python文件，可调用doc_generator.py生成markdown文档，并可以WEB在线渲染。
2. 前端markdown渲染界面支持"mermaid"时序图的展示
3. 前端界面提供下拉框，可选择本项目目录下的xlsx文件或csv文件，如果对应的md文件已经存在，则直接渲染，否则需要生成markdown文件后再渲染。
4. 已经生成markdown的xlsx文件或csv文件，可以选择重新生成markdown文件。
5. 输出目录结构调整为：
```markdown
## 2.1 一级功能需求 （对应一级模块）
 (该一级模块的功能简介，描述)
### 2.1.1 关键时序图/业务逻辑图
 (顺序列出各三级模块名称及时序图)
（三级模块时序图：可结合功能过程、子过程、触发事件、数据组等相关信息，生成可渲染的Mermaid语法图）
### 2.1.2 功能需求描述
（详细描述该一级模块的功能）
#### 2.1.2.1 二级功能需求 （对应二级模块）
##### 2.1.2.1.1 三级功能需求 （对应三级模块）
{三级模块名称} 包含如下功能：
{顺序列出该三级模块所包含的各功能过程名称}
###### 2.1.2.1.1.1 功能过程 （对应功能过程）
***功能简介***
{功能过程名称}
***功能要求***
{顺序列出各子过程描述列的名称}

## 2.2 一级功能需求 （对应一级模块）
....（省略）

```
6. 根据新的目录结构优化提示词，并约束：只为三级模块生成时序图，功能过程也生成了时序图。
7. 由于是按照三级模块调用大模型生成的时序图，可以从三级模块逐层向上组织文档内容,形成一级模块的功能描述和时序图。

## 改进
1.当前的WEB UI不支持sequenceDiagram时序图，如图，增加这个时序图渲染支持。
2.选择csv/xlsx文件后，如果有markdown文件，可直接在当前页面渲染，不用打开新的标签页。
3.一级模块的功能需求描述是空的，如图2.1.2和2.1.2.1 之间需要有文字性的功能需求描述，可根据各子级模块的名称进行综合描述并补充到这里。